// For more information about this file see https://dove.feathersjs.com/guides/cli/channels.html
import type {RealTimeConnection, Params} from '@feathersjs/feathers';
import type {AuthenticationResult} from '@feathersjs/authentication';
import '@feathersjs/transport-commons';
import type {Application, HookContext} from './declarations.js';
import {logger} from './logger.js';

export const channels = (app: Application) => {
    logger.warn(
        'Publishing all events to all authenticated users. See `channels.js` and https://dove.feathersjs.com/api/channels.html for more information.'
    );

    app.on('connection', (connection: RealTimeConnection) => {
        // On a new real-time connection, add it to the anonymous channel
        app.channel('anonymous').join(connection);
    });

    // app.service('shops').publish((shop) => {
    //
    // })
    app.service('shops').on('patched', (shop, { params }) => {
        if (shop._id && params.connection) {
            app.channel(`shops/${shop._id}`).join(params.connection)
        }
    })
    app.service('shops').on('created', (shop, { params }) => {
        if (shop._id && params.connection) {
            app.channel(`shops/${shop._id}`).join(params.connection)
        }
    })
    app.service('ims').on('patched', (im, { params }) => {
        if (im._id && params.connection) {
            app.channel(`ims/${im._id}`).join(params.connection)
        }
    })
    app.service('ims').on('created', (im, { params }) => {
        if (im._id && params.connection) {
            app.channel(`ims/${im._id}`).join(params.connection)
        }
    })


    app.on(
        'login',
        (authResult: AuthenticationResult, {connection}: Params) => {
            // connection can be undefined if there is no
            // real-time connection, e.g. when logging in via REST
            if (connection) {
                // The connection is no longer anonymous, remove it
                app.channel('anonymous').leave(connection);

                // Add it to the authenticated user channel
                app.channel('authenticated').join(connection);
            }
        }
    );

    // eslint-disable-next-line no-unused-vars
    app.publish((data: any, context: HookContext) => {
        // Here you can add event publishers to channels set up in `channels.js`
        // To publish only for a specific event use `app.publish(eventname, () => {})`
        app.service('shops').publish('patched', (data: any) => {
            if(data._id) return app.channel(`shops/${data._id}`);
        })
        app.service('ims').publish('patched', (data: any) => {
            if(data._id) return app.channel(`ims/${data._id}`);
        })
        // e.g. to publish all service events to all authenticated users use
        return app.channel('authenticated');
    });
};
