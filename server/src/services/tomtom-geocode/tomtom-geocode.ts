// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html

import type { Application } from '../../declarations.js'
import { TomtomGeocodeService, getOptions } from './tomtom-geocode.class.js'
import { tomtomGeocodePath, tomtomGeocodeMethods } from './tomtom-geocode.shared.js'

export * from './tomtom-geocode.class.js'
const successHook = context => {
  context.result.data = context.result.results;
  context.result.total = 0;
  context.result.limit = 400;
  context.result.skip = 0;
};
// A configure function that registers the service and its hooks via `app.configure`
export const tomtomGeocode = (app: Application) => {
  // Register our service on the Feathers application
  app.use(tomtomGeocodePath, new TomtomGeocodeService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: tomtomGeocodeMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(tomtomGeocodePath).hooks({
    around: {
      all: []
    },
    before: {
      all: [],
      find: [],
      get: [],
      create: [],
      patch: [],
      remove: []
    },
    after: {
      all: [],
      find: [successHook],
      create: [successHook]
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [tomtomGeocodePath]: TomtomGeocodeService
  }
}
