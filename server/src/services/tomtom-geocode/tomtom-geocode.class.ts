// For more information about this file see https://dove.feathersjs.com/guides/cli/service.class.html#custom-services
import type { Id, NullableId, Params, ServiceInterface } from '@feathersjs/feathers'

import type { Application } from '../../declarations.js'
import axios from "axios";

type TomtomGeocode = any
type TomtomGeocodeData = any
type TomtomGeocodePatch = any
type TomtomGeocodeQuery = any

export type { TomtomGeocode, TomtomGeocodeData, TomtomGeocodePatch, TomtomGeocodeQuery }

export interface TomtomGeocodeServiceOptions {
  app: Application
}

export interface TomtomGeocodeParams extends Params<TomtomGeocodeQuery> {}
const key = process.env.TOMTOM_API_KEY || '********************************';
export { key as tomtomKey }
export const apiClient = axios.create({
  baseURL: 'https://api.tomtom.com',
  withCredentials: false, // This is the default
  // headers: {
  //   'Accept': 'application/json',
  //   'Content-Type': 'application/json'
  // }
});
// This is a skeleton for a custom service class. Remove or add the methods you need here
export class TomtomGeocodeService<ServiceParams extends TomtomGeocodeParams = TomtomGeocodeParams>
  implements ServiceInterface<TomtomGeocode, TomtomGeocodeData, ServiceParams, TomtomGeocodePatch>
{
  constructor(public options: TomtomGeocodeServiceOptions) {}

  async find(_params?: ServiceParams): Promise<TomtomGeocode[]> {
    let queryParams = {
      key: key,
      language: 'en-US',
      limit: 10,
      countrySet: 'US',
      typeahead: true,
    };
    return await apiClient.get('/search/2/geocode/' + encodeURI(_params?.query.text) + '.json',{'params': queryParams})
        .then(res => {
          // console.log('tomtom res', res.data);
          return res.data;
        })
        .catch(err => {
          // eslint-disable-next-line no-console
          console.error('geocode error', err.message);
          // throw new Error('Error searching - ' + err.message);
        });
  }

  async get(id: Id, _params?: ServiceParams): Promise<TomtomGeocode> {
    return {
      id: 0,
      text: `A new message with ID: ${id}!`
    }
  }

  async create(data: TomtomGeocodeData, params?: ServiceParams): Promise<TomtomGeocode>
  async create(data: TomtomGeocodeData[], params?: ServiceParams): Promise<TomtomGeocode[]>
  async create(
    data: TomtomGeocodeData | TomtomGeocodeData[],
    params?: ServiceParams
  ): Promise<TomtomGeocode | TomtomGeocode[]> {
    if (Array.isArray(data)) {
      return Promise.all(data.map((current) => this.create(current, params)))
    }

    return {
      id: 0,
      ...data
    }
  }

  // This method has to be added to the 'methods' option to make it available to clients
  async update(id: NullableId, data: TomtomGeocodeData, _params?: ServiceParams): Promise<TomtomGeocode> {
    return {
      id: 0,
      ...data
    }
  }

  async patch(id: NullableId, data: TomtomGeocodePatch, _params?: ServiceParams): Promise<TomtomGeocode> {
    return {
      id: 0,
      text: `Fallback for ${id}`,
      ...data
    }
  }

  async remove(id: NullableId, _params?: ServiceParams): Promise<TomtomGeocode> {
    return {
      id: 0,
      text: 'removed'
    }
  }
}

export const getOptions = (app: Application) => {
  return { app }
}
