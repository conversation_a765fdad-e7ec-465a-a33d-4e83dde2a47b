export const passwordReset = (PIN: string, loginId: string): string => {
    return `<!DOCTYPE html>
<html>
<head>
  <title>Reset Your Password</title>
</head>
<body style="font-family: Arial, sans-serif; background-color: #f5f5f5; padding: 20px;">
  <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 20px; border-radius: 8px;">
    <h2 style="color: #333333;">Reset Your Password</h2>
    <p style="color: #333333;">Hello,</p>
    <p style="color: #333333;">We received a request to reset your password for your Commoncare account. Use the PIN below to continue:</p>
    <p style="background-color: #eeeeee; padding: 10px; font-size: 20px; text-align: center; font-weight: bold;">${PIN}</p>
    <p style="color: #333333;">Or click the link below to reset your password:</p>
    <p><a href="https://commoncare.org/login/reset?id=${loginId}" style="color: #1a73e8;">Reset Password</a></p>
    <p style="color: #666666; font-size: 12px;">If you did not request a password reset, you can safely ignore this email.</p>
    <p style="color: #999999; font-size: 12px; margin-top: 20px;">- The Commoncare Team</p>
  </div>
</body>
</html>`;
};
