// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  ServicesExpose,
  ServicesExposeData,
  ServicesExposePatch,
  ServicesExposeQuery,
  ServicesExposeService
} from './services-expose.class.js'

export type { ServicesExpose, ServicesExposeData, ServicesExposePatch, ServicesExposeQuery }

export type ServicesExposeClientService = Pick<
  ServicesExposeService<Params<ServicesExposeQuery>>,
  (typeof servicesExposeMethods)[number]
>

export const servicesExposePath = 'services-expose'

export const servicesExposeMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const servicesExposeClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(servicesExposePath, connection.service(servicesExposePath), {
    methods: servicesExposeMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [servicesExposePath]: ServicesExposeClientService
  }
}
