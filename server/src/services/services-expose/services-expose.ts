// For more information about this file see https://dove.feathersjs.com/guides/cli/service.html
import { authenticate } from '@feathersjs/authentication'

import type {Application, HookContext} from '../../declarations.js'
import { ServicesExposeService, getOptions } from './services-expose.class.js'
import { servicesExposePath, servicesExposeMethods } from './services-expose.shared.js'

export * from './services-expose.class.js'
import { _get } from '../../utils/dash-utils.js';

const expose = (context:HookContext):HookContext => {
  const services = Object.keys(context.app.services);
  const modelArray:Array<{service: string, fields:Array<string>, _id: string}> = [];
  for(let i = 0; i < services.length; i++){
    const serviceHasModel = _get(context.app.service(services[i] as keyof typeof context.app.services), 'options.Model');
    if(!!serviceHasModel) modelArray.push({service: services[i], _id: services[i], fields: []});
  }
  context.result = { data: modelArray, total: modelArray.length };
  return context;
}

// A configure function that registers the service and its hooks via `app.configure`
export const servicesExpose = (app: Application) => {
  // Register our service on the Feathers application
  app.use(servicesExposePath, new ServicesExposeService(getOptions(app)), {
    // A list of all methods this service exposes externally
    methods: servicesExposeMethods,
    // You can add additional custom events to be sent to clients here
    events: []
  })
  // Initialize hooks
  app.service(servicesExposePath).hooks({
    around: {
      all: [authenticate('jwt')]
    },
    before: {
      all: [],
      find: [expose],
      get: [],
      create: [],
      patch: [],
      remove: []
    },
    after: {
      all: [],
      find: []
    },
    error: {
      all: []
    }
  })
}

// Add this service to the service type index
declare module '../../declarations.js' {
  interface ServiceTypes {
    [servicesExposePath]: ServicesExposeService
  }
}
