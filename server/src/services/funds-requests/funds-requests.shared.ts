// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  FundsRequests,
  FundsRequestsData,
  FundsRequestsPatch,
  FundsRequestsQuery,
  FundsRequestsService
} from './funds-requests.class.js'

export type { FundsRequests, FundsRequestsData, FundsRequestsPatch, FundsRequestsQuery }

export type FundsRequestsClientService = Pick<
  FundsRequestsService<Params<FundsRequestsQuery>>,
  (typeof fundsRequestsMethods)[number]
>

export const fundsRequestsPath = 'funds-requests'

export const fundsRequestsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const fundsRequestsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(fundsRequestsPath, connection.service(fundsRequestsPath), {
    methods: fundsRequestsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [fundsRequestsPath]: FundsRequestsClientService
  }
}
