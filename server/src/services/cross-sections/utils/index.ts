import {CoreCall} from 'feathers-ucan';
import {HookContext} from '../../../declarations.js';
import {_get} from 'symbol-ucan';

type Opts = {
    throw?: boolean
}
//hackIdList = 'plan_conditions:plans, plan_procedures:plans, plan_meds:plans, provider_docs:providers, plan_providers:plans, household_providers:households, patient_providers:ppls, 'provider_patients:providers
export const loadOrCreate = (hackId: string, sectionObj: any, options?: Opts) => {
    return async (context: HookContext) => {
        const ex = await new CoreCall('cross-sections', context).find({query: {hackId, $limit: 1}})
            .catch(err => {
                if (options?.throw) throw new Error(`Error finding cross sectional data for ${hackId}: ${err.message}`)
                return undefined
            });
        ;
        if (ex.data?.length) {
            const $addToSet = {}
            for (const k in sectionObj) {
                if (Array.isArray(sectionObj[k])) $addToSet[`sections.${k}`] = {$each: sectionObj[k]};
                else $addToSet[`sections.${k}`] = sectionObj[k];
            }
            return await new CoreCall('cross-sections', context).patch(ex.data[0]._id, {$addToSet}, {admin_pass: true})
                .catch(err => {
                    if (options?.throw) throw new Error(`Error updating cross sectional data for ${hackId}: ${err.message}`)
                    return undefined
                });
        } else {
            const sections = {};
            for (const k in sectionObj) {
                if (!Array.isArray(sectionObj[k])) sections[k] = [sectionObj[k]];
            }
            const spl = hackId.split(':');
            return await new CoreCall('cross-sections', context).create({
                hackId,
                subject: spl[spl.length - 1],
                sections
            }, {admin_pass: true})
                .catch(err => {
                    if (options?.throw) throw new Error(`Error creating cross sectional data for ${hackId}: ${err.message}`)
                    return undefined
                });
        }
    }
}

export const hackIdBefore = (key: string, service: string, section:string) => {
    return async (context: HookContext) => {
        const id = _get(context.params, `hackId.${key}`);
        if (id) {
            const ids: Array<string> = [];
            const hackId = `${key}:${service}:${id}`;
            let crossSection;
            if (context.params.hackId[hackId]) crossSection = await new CoreCall('cross-sections', context).get(context.params.hackId[hackId]);
            else {
                const cs = await new CoreCall('cross-sections', context).find({query: {$limit: 1, hackId}});
                crossSection = cs?.data[0]
            }
            if (crossSection) {
                for (const cId of _get(crossSection, `sections.${section}`) as Array<string> || []) {
                    ids.push(cId)
                }
                context.params.hackId = {...context.params.hackId, [hackId]: crossSection._id }
            }
            context.params.query._id = {$in: ids}
        }
        if(!context.params.query.$sort) context.params.query.$sort = { code: -1}
        return context;
    }
}

export const hackIdAfter = (key: string, service: string) => {
    return context => {
        const id = _get(context.params, `hackId.${key}`);
        if (id) {
            const hackId = `${key}:${service}:${id}`;
            const hasHackId = context.params.hackId[hackId];
            if (hasHackId) {
                const join = (val) => {
                    val._fastjoin = {...val._fastjoin, [hackId]: hasHackId};
                    return val;
                }
                if (context.method === 'find') context.result.data = context.result.data?.map(a => join(a));
                else context.result = join(context.result);
            }
        }
        return context;
    }
}
