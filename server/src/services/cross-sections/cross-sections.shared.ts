// For more information about this file see https://dove.feathersjs.com/guides/cli/service.shared.html
import type { Params } from '@feathersjs/feathers'
import type { ClientApplication } from '../../client.js'
import type {
  CrossSections,
  CrossSectionsData,
  CrossSectionsPatch,
  CrossSectionsQuery,
  CrossSectionsService
} from './cross-sections.class.js'

export type { CrossSections, CrossSectionsData, CrossSectionsPatch, CrossSectionsQuery }

export type CrossSectionsClientService = Pick<
  CrossSectionsService<Params<CrossSectionsQuery>>,
  (typeof crossSectionsMethods)[number]
>

export const crossSectionsPath = 'cross-sections'

export const crossSectionsMethods = ['find', 'get', 'create', 'patch', 'remove'] as const

export const crossSectionsClient = (client: ClientApplication) => {
  const connection = client.get('connection')

  client.use(crossSectionsPath, connection.service(crossSectionsPath), {
    methods: crossSectionsMethods
  })
}

// Add this service to the client service type index
declare module '../../client.js' {
  interface ServiceTypes {
    [crossSectionsPath]: CrossSectionsClientService
  }
}
